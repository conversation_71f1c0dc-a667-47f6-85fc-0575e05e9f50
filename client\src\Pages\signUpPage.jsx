import React, { useEffect } from "react";
import SignUp from "../Components/SignUp";
import { useNavigate } from "react-router-dom";
import { auth } from "../Config/firebase";

const SignUpPage = () => {
  const navigate = useNavigate();

  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged((user) => {
      if (user) {
        navigate("/");
      }
    });

    return () => unsubscribe();
  }, [navigate]);

  return (
    <div className="signup-page-container">
      <div className="signup-card">
        <SignUp />
      </div>
    </div>
  );
};

export default SignUpPage;

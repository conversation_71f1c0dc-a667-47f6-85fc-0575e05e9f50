import { useEffect } from "react";
import SignUp from "../Components/SignUp";
import { useNavigate } from "react-router-dom";
import { auth } from "../Config/firebase";

const SignUpPage = () => {
  const navigate = useNavigate();

  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged((user) => {
      if (user) {
        navigate("/");
      }
    });

    return () => unsubscribe();
  }, [navigate]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full">
        <SignUp />
      </div>
    </div>
  );
};

export default SignUpPage;

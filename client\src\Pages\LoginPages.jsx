import React, { useEffect } from "react";
import Login from "../Components/LoginInput";
import { useNavigate } from "react-router-dom";
import { auth } from "../Config/firebase";

const LoginPages = () => {
  const navigate = useNavigate();

  useEffect(() => {

    const unsubscribe = auth.onAuthStateChanged((user) => {
      if (user) {

        navigate("/");
      }
    });

    return () => unsubscribe();
  }, [navigate]);

  return (
    <div className="login-page-container">
      <div className="login-card">
        <Login />
      </div>
    </div>
  );
};

export default LoginPages;
